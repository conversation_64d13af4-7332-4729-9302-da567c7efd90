2025-08-20 16:56:06 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-20 16:56:06 | ERROR    | database.connection:connect:38 - Failed to connect to SurrealDB: Surreal() missing 1 required positional argument: 'url'
2025-08-20 16:56:06 | ERROR    | main:lifespan:38 - Failed to connect to database: Surreal() missing 1 required positional argument: 'url'
2025-08-21 00:02:41 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-21 00:02:41 | ERROR    | database.connection:connect:42 - Failed to connect to SurrealDB: Unsupported protocol in URL: mem://. Use 'ws://' or 'http://'.
2025-08-21 00:02:41 | ERROR    | main:lifespan:40 - Failed to connect to database: Unsupported protocol in URL: mem://. Use 'ws://' or 'http://'.
2025-08-21 00:06:41 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-21 00:06:41 | WARNING  | database.connection:connect:38 - SurrealDB connection failed: did not receive a valid HTTP response
2025-08-21 00:06:41 | WARNING  | database.connection:connect:39 - Running in mock mode - database operations will be simulated
2025-08-21 00:06:41 | INFO     | main:lifespan:38 - ✓ Database connection established
2025-08-21 00:06:41 | INFO     | main:lifespan:51 - No pre-trained models found - will train on first use
2025-08-21 00:06:41 | INFO     | main:lifespan:58 - ✓ Application startup completed
2025-08-21 00:09:12 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-21 00:09:12 | WARNING  | database.connection:connect:38 - SurrealDB connection failed: did not receive a valid HTTP response
2025-08-21 00:09:12 | WARNING  | database.connection:connect:39 - Running in mock mode - database operations will be simulated
2025-08-21 00:09:12 | INFO     | main:lifespan:38 - ✓ Database connection established
2025-08-21 00:09:12 | INFO     | main:lifespan:51 - No pre-trained models found - will train on first use
2025-08-21 00:09:12 | INFO     | main:lifespan:58 - ✓ Application startup completed
2025-08-21 00:10:07 | INFO     | utils.logging:setup_logging:55 - Logging system initialized
2025-08-21 00:10:08 | WARNING  | database.connection:connect:38 - SurrealDB connection failed: did not receive a valid HTTP response
2025-08-21 00:10:08 | WARNING  | database.connection:connect:39 - Running in mock mode - database operations will be simulated
2025-08-21 00:10:08 | INFO     | main:lifespan:38 - ✓ Database connection established
2025-08-21 00:10:08 | INFO     | main:lifespan:51 - No pre-trained models found - will train on first use
2025-08-21 00:10:08 | INFO     | main:lifespan:58 - ✓ Application startup completed
2025-08-21 00:10:08 | INFO     | main:lifespan:63 - Shutting down application...
2025-08-21 00:10:08 | INFO     | main:lifespan:67 - ✓ Database connection closed
2025-08-21 00:10:08 | INFO     | main:lifespan:69 - ✓ Application shutdown completed
