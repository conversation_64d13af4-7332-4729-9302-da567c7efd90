import asyncio
from typing import Optional, Dict, Any, List
from surrealdb import Surreal
from config import settings
from utils.logging import logger

class SurrealDBManager:
    """SurrealDB connection and query management"""
    
    def __init__(self, db_url: str = None):
        self.db_url = db_url or settings.surrealdb_url
        self.db: Optional[Surreal] = None
        self.connected = False
    
    async def connect(self):
        """Initialize database connection and schema"""
        try:
            # For SurrealDB 1.0.6+, URL is required in constructor
            self.db = Surreal(self.db_url)

            # Authenticate if credentials provided
            if settings.surrealdb_username and settings.surrealdb_password:
                await self.db.signin({
                    "user": settings.surrealdb_username,
                    "pass": settings.surrealdb_password
                })

            # Select namespace and database
            await self.db.use(settings.surrealdb_namespace, settings.surrealdb_database)

            # Initialize schema
            await self._initialize_schema()

            self.connected = True
            logger.info("✓ SurrealDB connected successfully")

        except Exception as e:
            logger.warning(f"SurrealDB connection failed: {e}")
            logger.warning("Running in mock mode - database operations will be simulated")
            self.db = None
            self.connected = False
            # Don't raise the exception to allow the app to start
    
    async def disconnect(self):
        """Close database connection"""
        if self.db:
            await self.db.close()
            self.connected = False
            logger.info("SurrealDB connection closed")
    
    async def _initialize_schema(self):
        """Initialize database schema if not exists"""
        schema_queries = [
            # Patients table
            """
            DEFINE TABLE patients SCHEMAFULL
              PERMISSIONS 
                FOR select, update WHERE $scope = "clinician_scope"
                FOR create, delete WHERE $scope = "clinician_scope" AND role CONTAINS "admin";
            """,
            
            # Patient fields
            """
            DEFINE FIELD pid ON patients TYPE string ASSERT $value != NONE;
            DEFINE FIELD search_token ON patients TYPE string;
            DEFINE FIELD demographics ON patients TYPE object;
            DEFINE FIELD symptoms ON patients TYPE object DEFAULT {};
            DEFINE FIELD clinical_history ON patients TYPE object DEFAULT {};
            DEFINE FIELD created_at ON patients TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated_at ON patients TYPE datetime DEFAULT time::now();
            DEFINE FIELD clinician_id ON patients TYPE string;
            DEFINE FIELD is_locked ON patients TYPE bool DEFAULT false;
            DEFINE FIELD completeness_score ON patients TYPE float DEFAULT 0.0;
            DEFINE FIELD ml_features_cache ON patients TYPE object DEFAULT {};
            """,
            
            # Indexes
            """
            DEFINE INDEX unique_pid ON patients FIELDS pid UNIQUE;
            DEFINE INDEX search_token_idx ON patients FIELDS search_token UNIQUE;
            DEFINE INDEX clinician_idx ON patients FIELDS clinician_id;
            """,
            
            # Lab results table
            """
            DEFINE TABLE lab_results SCHEMAFULL;
            DEFINE FIELD patient ON lab_results TYPE record<patients>;
            DEFINE FIELD lab_type ON lab_results TYPE string;
            DEFINE FIELD collection_date ON lab_results TYPE datetime;
            DEFINE FIELD raw_values ON lab_results TYPE object;
            DEFINE FIELD normalized_values ON lab_results TYPE object DEFAULT {};
            DEFINE FIELD abnormal_flags ON lab_results TYPE object DEFAULT {};
            DEFINE FIELD derived_ratios ON lab_results TYPE object DEFAULT {};
            DEFINE FIELD created_at ON lab_results TYPE datetime DEFAULT time::now();
            """,
            
            # ML predictions table
            """
            DEFINE TABLE ml_predictions SCHEMAFULL;
            DEFINE FIELD patient ON ml_predictions TYPE record<patients>;
            DEFINE FIELD prediction_type ON ml_predictions TYPE string;
            DEFINE FIELD prediction_value ON ml_predictions TYPE string;
            DEFINE FIELD confidence_score ON ml_predictions TYPE float;
            DEFINE FIELD probability_distribution ON ml_predictions TYPE object;
            DEFINE FIELD prediction_timestamp ON ml_predictions TYPE datetime DEFAULT time::now();
            DEFINE FIELD clinician_feedback ON ml_predictions TYPE string;
            """
        ]
        
        for query in schema_queries:
            try:
                await self.db.query(query.strip())
            except Exception as e:
                # Some errors are expected (e.g., table already exists)
                logger.debug(f"Schema query result: {e}")
    
    async def create_patient(self, patient_data: Dict[str, Any]) -> str:
        """Create new patient record"""
        if not self.connected or self.db is None:
            logger.info("Mock mode: Simulating patient creation")
            return f"mock:patient:{patient_data.get('pid', 'unknown')}"

        try:
            result = await self.db.create("patients", patient_data)
            return result[0]["id"] if result else None
        except Exception as e:
            logger.error(f"Failed to create patient: {e}")
            raise

    async def get_patient(self, pid: str) -> Optional[Dict[str, Any]]:
        """Retrieve patient by PID"""
        if not self.connected or self.db is None:
            logger.info(f"Mock mode: Simulating get patient {pid}")
            return None

        try:
            query = "SELECT * FROM patients WHERE pid = $pid"
            result = await self.db.query(query, {"pid": pid})
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get patient {pid}: {e}")
            return None

    async def update_patient(self, pid: str, update_data: Dict[str, Any]) -> bool:
        """Update patient record"""
        if not self.connected or self.db is None:
            logger.info(f"Mock mode: Simulating update patient {pid}")
            return True

        try:
            # Add updated_at timestamp
            update_data["updated_at"] = "time::now()"

            query = "UPDATE patients SET $data WHERE pid = $pid"
            result = await self.db.query(query, {"pid": pid, "data": update_data})
            return len(result) > 0
        except Exception as e:
            logger.error(f"Failed to update patient {pid}: {e}")
            return False

    async def search_patients(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search patients with filters"""
        if not self.connected or self.db is None:
            logger.info("Mock mode: Simulating patient search")
            return []

        try:
            # Build dynamic query based on filters
            conditions = []
            params = {}

            if "clinician_id" in filters:
                conditions.append("clinician_id = $clinician_id")
                params["clinician_id"] = filters["clinician_id"]

            if "date_from" in filters:
                conditions.append("created_at >= $date_from")
                params["date_from"] = filters["date_from"]

            if "date_to" in filters:
                conditions.append("created_at <= $date_to")
                params["date_to"] = filters["date_to"]

            where_clause = " AND ".join(conditions) if conditions else "true"
            query = f"SELECT * FROM patients WHERE {where_clause} ORDER BY created_at DESC"

            result = await self.db.query(query, params)
            return result
        except Exception as e:
            logger.error(f"Failed to search patients: {e}")
            return []

    async def get_ml_training_data(self) -> List[Dict[str, Any]]:
        """Retrieve complete data for ML training"""
        if not self.connected or self.db is None:
            logger.info("Mock mode: Simulating ML training data retrieval")
            return []

        try:
            query = """
            SELECT
              pid,
              demographics,
              symptoms,
              clinical_history,
              (SELECT * FROM lab_results WHERE patient = $parent.id
               ORDER BY collection_date DESC LIMIT 5) AS recent_labs,
              completeness_score
            FROM patients
            WHERE is_locked = true AND completeness_score > 0.7
            """
            result = await self.db.query(query)
            return result
        except Exception as e:
            logger.error(f"Failed to get ML training data: {e}")
            return []

    async def store_ml_prediction(self, prediction_data: Dict[str, Any]) -> str:
        """Store ML prediction result"""
        if not self.connected or self.db is None:
            logger.info("Mock mode: Simulating ML prediction storage")
            return f"mock:prediction:{prediction_data.get('prediction_type', 'unknown')}"

        try:
            result = await self.db.create("ml_predictions", prediction_data)
            return result[0]["id"] if result else None
        except Exception as e:
            logger.error(f"Failed to store ML prediction: {e}")
            raise
