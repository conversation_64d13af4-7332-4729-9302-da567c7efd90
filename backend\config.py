from pydantic_settings import BaseSettings
from typing import List, Optional
import os
import secrets
from pydantic import Field

class Settings(BaseSettings):
    # Application settings
    app_name: str = "Psychiatric ML Data Collection"
    debug: bool = False
    environment: str = "development"
    log_level: str = "INFO"
    
    # Database configuration
    surrealdb_url: str = "ws://localhost:8000/rpc"
    surrealdb_namespace: str = "psychiatric"
    surrealdb_database: str = "research_data"
    surrealdb_username: Optional[str] = None
    surrealdb_password: Optional[str] = None
    
    # Security configuration - no defaults, must be set via environment
    secret_key: str = Field(default="", env='SECRET_KEY')
    clinic_secret: str = Field(default="", env='CLINIC_SECRET')
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 480
    
    # ML configuration
    ml_models_path: str = "./models"
    enable_realtime_predictions: bool = True
    min_training_samples: int = 100
    model_retrain_threshold: float = 0.7
    
    # API configuration
    api_v1_prefix: str = "/api/v1"
    cors_origins: List[str] = ["http://localhost:3000"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Generate secure random keys for development if none provided
        if self.environment == "development":
            if not self.secret_key:
                self.secret_key = secrets.token_urlsafe(32)
                print(f"Generated development SECRET_KEY: {self.secret_key}")
            
            if not self.clinic_secret:
                self.clinic_secret = secrets.token_urlsafe(32)
                print(f"Generated development CLINIC_SECRET: {self.clinic_secret}")
        else:
            # Production validation - refuse to run with empty secrets
            if not self.secret_key:
                raise ValueError(
                    "SECRET_KEY environment variable is required for production"
                )
            
            if not self.clinic_secret:
                raise ValueError(
                    "CLINIC_SECRET environment variable is required for production"
                )

settings = Settings()
